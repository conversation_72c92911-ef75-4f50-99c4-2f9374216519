import { createContext, ReactNode, useContext, useEffect, useMemo } from "react";
import { ChartStore } from "../store/chartStore";
import { TSignal, useSignal } from "../hooks/useSignal";
import { TRange } from "../types/common";
import { DEFAULT_CHART_PADDING_RATIO, } from "../commons/constants";
type TChartContext = {
    autoScale: TSignal<boolean>,
    valueRange: TSignal<TRange>
    chartStore: ChartStore;
    paneWidth: number;
    height: number;
    yAxisWidth: number;
}

export const ChartContext = createContext<TChartContext | undefined>(undefined);

export const useChartContext = () => {
    const context = useContext(ChartContext);
    if (!context) {
        throw new Error('useChartContext must be used within an ChartContextProvider');
    }
    return context;
}

export const ChartContextProvider = (props: { children: ReactNode, chartStore: ChartStore, height: number, paneWidth: number, yAxisWidth: number }) => {
    console.debug('rendering chart context provider')
    const autoScale = useSignal(true)
    const valueRange = useSignal<TRange>({ min: 0, max: 0 })
    useEffect(() => {
        if (!props.chartStore) return
        const timeRange = props.chartStore.getTimeRange()
        if (!timeRange) return
        const chartValueRange = props.chartStore.getValueRangeByTime(timeRange.min, timeRange.max)
        if (chartValueRange) {
            const span = chartValueRange.max - chartValueRange.min
            const padding = span * DEFAULT_CHART_PADDING_RATIO
            valueRange.value = {
                min: chartValueRange.min - padding,
                max: chartValueRange.max + padding
            }
        }
    }, [props.chartStore])


    const value: TChartContext = useMemo(() => ({
        paneWidth: props.paneWidth,
        height: props.height,
        yAxisWidth: props.yAxisWidth,
        autoScale,
        valueRange,
        chartStore: props.chartStore,
    }), [props.chartStore, autoScale, valueRange, props.paneWidth, props.height, props.yAxisWidth]);

    return (
        <ChartContext.Provider value={value}>
            {props.children}
        </ChartContext.Provider>
    );
}
import { Shape } from "react-konva";
import { type CandlestickStore } from "../../../store/candlestickStore";
import { usePaneScale } from "../../../hooks/useScale";
import Konva from "konva";
import { useAppContext } from "../../../contexts/appContext";
import { useChartContext } from "../../../contexts/chartContext";
import { useDataContext } from "../../../contexts/dataContext";

export const CandlestickPlot = (props: { store: CandlestickStore }) => {
    console.debug('rendering candlestick plot')
    const { store } = props
    const { timeRange, timeUnit } = useAppContext()
    const { valueRange } = useChartContext()
    const { ticker } = useDataContext()
    timeRange.use()
    valueRange.use()
    ticker.use()

    const { v2y, t2x, deltaT2x } = usePaneScale()



    const sceneFunc = (context: Konva.Context) => {
        const list = store.selectByTime(timeRange.value.min, timeRange.value.max)
        let barWidth = deltaT2x(timeUnit) - (store.config?.barSpacing || 3);

        barWidth = barWidth <= 1 ? 1 : barWidth;
        const cornerRadius = Math.min(4, barWidth / 4);

        for (const candle of list) {
            const closeY = v2y(candle.close);
            const openY = v2y(candle.open);
            const x = t2x(candle.time);
            const highY = v2y(candle.high);
            const lowY = v2y(candle.low);
            const change = candle.close - candle.open
            const color = candle.color || (change > 0 ? store.config?.riseColor : store.config?.fallColor)
            const isBull = change > 0;

            if (barWidth <= 2) {
                // Simple line for very thin bars
                context.beginPath();
                context.moveTo(x, highY);
                context.lineTo(x, lowY);
                context.closePath();
                context.setAttr('strokeStyle', color);
                context.setAttr('lineWidth', barWidth);
                context.stroke();
            } else {
                // Draw clean wicks
                const wickWidth = Math.min(barWidth, 2);
                context.setAttr('strokeStyle', color);
                context.setAttr('lineWidth', wickWidth);

                // Upper wick
                context.beginPath();
                context.moveTo(x, highY);
                context.lineTo(x, Math.min(closeY, openY));
                context.stroke();

                // Lower wick
                context.beginPath();
                context.moveTo(x, Math.max(closeY, openY));
                context.lineTo(x, lowY);
                context.stroke();

                // Draw body with gradient
                const bodyX = x - barWidth / 2;
                const bodyY = Math.min(closeY, openY);
                const bodyHeight = Math.max(Math.abs(closeY - openY), 1);

                // Create gradient
                const gradient = context.createLinearGradient(
                    bodyX,
                    isBull ? bodyY + bodyHeight : bodyY, // Start from bottom for bull, top for bear
                    bodyX,
                    isBull ? bodyY : bodyY + bodyHeight  // End at top for bull, bottom for bear
                );

                // Add gradient stops with opacity variation
                if (isBull) {
                    // Bull: from bottom (more opaque) to top (less opaque)
                    gradient.addColorStop(0, color + 'FF'); // Full opacity at bottom
                    gradient.addColorStop(1, color + '80'); // 50% opacity at top
                } else {
                    // Bear: from top (more opaque) to bottom (less opaque)
                    gradient.addColorStop(0, color + 'FF'); // Full opacity at top
                    gradient.addColorStop(1, color + '80'); // 50% opacity at bottom
                }

                // Draw rounded rectangle body
                context.beginPath();
                context.moveTo(bodyX + cornerRadius, bodyY);
                context.lineTo(bodyX + barWidth - cornerRadius, bodyY);
                context.arcTo(bodyX + barWidth, bodyY, bodyX + barWidth, bodyY + cornerRadius, cornerRadius);
                context.lineTo(bodyX + barWidth, bodyY + bodyHeight - cornerRadius);
                context.arcTo(bodyX + barWidth, bodyY + bodyHeight, bodyX + barWidth - cornerRadius, bodyY + bodyHeight, cornerRadius);
                context.lineTo(bodyX + cornerRadius, bodyY + bodyHeight);
                context.arcTo(bodyX, bodyY + bodyHeight, bodyX, bodyY + bodyHeight - cornerRadius, cornerRadius);
                context.lineTo(bodyX, bodyY + cornerRadius);
                context.arcTo(bodyX, bodyY, bodyX + cornerRadius, bodyY, cornerRadius);
                context.closePath();

                context.setAttr('fillStyle', gradient);
                context.fill();
            }
        }
    }


    return (
        <Shape
            sceneFunc={sceneFunc}
        />
    )
}

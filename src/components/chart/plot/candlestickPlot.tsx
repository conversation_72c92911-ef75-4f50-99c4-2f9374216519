import { Shape } from "react-konva";
import { type CandlestickStore } from "../../../store/candlestickStore";
import { usePaneScale } from "../../../hooks/useScale";
import Konva from "konva";
import { useAppContext } from "../../../contexts/appContext";
import { useChartContext } from "../../../contexts/chartContext";
import { useDataContext } from "../../../contexts/dataContext";

export const CandlestickPlot = (props: { store: CandlestickStore }) => {
    console.debug('rendering candlestick plot')
    const { store } = props
    const { timeRange, timeUnit } = useAppContext()
    const { valueRange } = useChartContext()
    const { ticker } = useDataContext()
    timeRange.use()
    valueRange.use()
    ticker.use()

    const { v2y, t2x, deltaT2x } = usePaneScale()



    const sceneFunc = (context: Konva.Context) => {
        const list = store.selectByTime(timeRange.value.min, timeRange.value.max)
        let barWidth = deltaT2x(timeUnit) - (store.config?.barSpacing || 3);

        barWidth = barWidth <= 1 ? 1 : barWidth;
        const cornerRadius = Math.min(4, barWidth / 4);

        for (const candle of list) {
            const closeY = v2y(candle.close);
            const openY = v2y(candle.open);
            const x = t2x(candle.time);
            const highY = v2y(candle.high);
            const lowY = v2y(candle.low);
            const change = candle.close - candle.open
            const color = candle.color || (change > 0 ? store.config?.riseColor : store.config?.fallColor)
            const isBull = change > 0;

            if (barWidth <= 2) {
                // Simple line for very thin bars
                context.beginPath();
                context.moveTo(x, highY);
                context.lineTo(x, lowY);
                context.closePath();
                context.setAttr('strokeStyle', color);
                context.setAttr('lineWidth', barWidth);
                context.stroke();
            } else {
                // Draw body with gradient first to determine wick colors
                const bodyX = x - barWidth / 2;
                const bodyY = Math.min(closeY, openY);
                const bodyHeight = Math.max(Math.abs(closeY - openY), 1);

                // Create gradient - Bull: bottom to top, Bear: top to bottom
                const gradient = context.createLinearGradient(
                    bodyX,
                    bodyY,           // Always start from top of body
                    bodyX,
                    bodyY + bodyHeight  // Always end at bottom of body
                );

                // Add gradient stops with opacity variation
                if (isBull) {
                    // Bull: gradient from bottom (opaque) to top (transparent)
                    gradient.addColorStop(0, color + '80'); // 50% opacity at top
                    gradient.addColorStop(1, color + 'FF'); // Full opacity at bottom
                } else {
                    // Bear: gradient from top (opaque) to bottom (transparent)
                    gradient.addColorStop(0, color + 'FF'); // Full opacity at top
                    gradient.addColorStop(1, color + '80'); // 50% opacity at bottom
                }

                // Determine wick colors based on gradient ends
                const upperWickColor = isBull ? color + '80' : color + 'FF'; // Top end of gradient
                const lowerWickColor = isBull ? color + 'FF' : color + '80'; // Bottom end of gradient

                // Draw wicks with appropriate colors
                const wickWidth = Math.min(barWidth, 2);
                context.setAttr('lineWidth', wickWidth);

                // Upper wick - use top gradient color
                context.setAttr('strokeStyle', upperWickColor);
                context.beginPath();
                context.moveTo(x, highY);
                context.lineTo(x, Math.min(closeY, openY));
                context.stroke();

                // Lower wick - use bottom gradient color
                context.setAttr('strokeStyle', lowerWickColor);
                context.beginPath();
                context.moveTo(x, Math.max(closeY, openY));
                context.lineTo(x, lowY);
                context.stroke();

                // Draw rounded rectangle body
                context.beginPath();
                context.moveTo(bodyX + cornerRadius, bodyY);
                context.lineTo(bodyX + barWidth - cornerRadius, bodyY);
                context.arcTo(bodyX + barWidth, bodyY, bodyX + barWidth, bodyY + cornerRadius, cornerRadius);
                context.lineTo(bodyX + barWidth, bodyY + bodyHeight - cornerRadius);
                context.arcTo(bodyX + barWidth, bodyY + bodyHeight, bodyX + barWidth - cornerRadius, bodyY + bodyHeight, cornerRadius);
                context.lineTo(bodyX + cornerRadius, bodyY + bodyHeight);
                context.arcTo(bodyX, bodyY + bodyHeight, bodyX, bodyY + bodyHeight - cornerRadius, cornerRadius);
                context.lineTo(bodyX, bodyY + cornerRadius);
                context.arcTo(bodyX, bodyY, bodyX + cornerRadius, bodyY, cornerRadius);
                context.closePath();

                context.setAttr('fillStyle', gradient);
                context.fill();
            }
        }
    }


    return (
        <Shape
            sceneFunc={sceneFunc}
        />
    )
}

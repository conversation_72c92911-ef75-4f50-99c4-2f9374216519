import { Shape } from "react-konva";
import { type CandlestickStore } from "../../../store/candlestickStore";
import { usePaneScale } from "../../../hooks/useScale";
import Konva from "konva";
import { useAppContext } from "../../../contexts/appContext";
import { useChartContext } from "../../../contexts/chartContext";
import { useDataContext } from "../../../contexts/dataContext";

export const CandlestickPlot = (props: { store: CandlestickStore }) => {
    console.debug('rendering candlestick plot')
    const { store } = props
    const { timeRange, timeUnit } = useAppContext()
    const { valueRange } = useChartContext()
    const { ticker } = useDataContext()
    timeRange.use()
    valueRange.use()
    ticker.use()

    const { v2y, t2x, deltaT2x } = usePaneScale()



    const sceneFunc = (context: Konva.Context) => {
        const list = store.selectByTime(timeRange.value.min, timeRange.value.max)
        let barWidth = deltaT2x(timeUnit) - (store.config?.barSpacing || 3);

        barWidth = barWidth <= 1 ? 1 : barWidth;
        const cornerRadius = Math.min(4, barWidth / 4);

        for (const candle of list) {
            const closeY = v2y(candle.close);
            const openY = v2y(candle.open);
            const x = t2x(candle.time);
            const highY = v2y(candle.high);
            const lowY = v2y(candle.low);
            const change = candle.close - candle.open
            const color = candle.color || (change > 0 ? store.config?.riseColor : store.config?.fallColor)
            const isBull = change > 0;

            if (barWidth <= 2) {
                // Simple line for very thin bars
                context.beginPath();
                context.moveTo(x, highY);
                context.lineTo(x, lowY);
                context.closePath();
                context.setAttr('strokeStyle', color);
                context.setAttr('lineWidth', barWidth);
                context.stroke();
            } else {
                // Draw body with gradient first to determine wick colors
                const bodyX = x - barWidth / 2;
                const bodyY = Math.min(closeY, openY);
                const bodyHeight = Math.max(Math.abs(closeY - openY), 1);
                const minHeightForRoundCorners = cornerRadius * 2;

                // Create gradient - Bull: bottom to top, Bear: top to bottom
                const gradient = context.createLinearGradient(
                    bodyX,
                    bodyY,           // Always start from top of body
                    bodyX,
                    bodyY + bodyHeight  // Always end at bottom of body
                );

                // Add gradient stops with opacity variation
                if (isBull) {
                    // Bull: gradient from bottom (opaque) to top (transparent)
                    gradient.addColorStop(0, color + '80'); // 50% opacity at top
                    gradient.addColorStop(1, color + 'FF'); // Full opacity at bottom
                } else {
                    // Bear: gradient from top (opaque) to bottom (transparent)
                    gradient.addColorStop(0, color + 'FF'); // Full opacity at top
                    gradient.addColorStop(1, color + '80'); // 50% opacity at bottom
                }

                // Determine wick colors based on gradient ends
                const upperWickColor = isBull ? color + '80' : color + 'FF'; // Top end of gradient
                const lowerWickColor = isBull ? color + 'FF' : color + '80'; // Bottom end of gradient

                // Create melting effect by drawing wicks with gradients that blend into body
                const wickWidth = Math.min(barWidth, 2);
                const meltDistance = Math.min(bodyHeight * 0.3, 10); // How far the melt extends into the body

                // Upper wick with melting gradient
                if (highY < Math.min(closeY, openY)) {
                    const wickGradient = context.createLinearGradient(
                        x, highY,
                        x, Math.min(closeY, openY) + meltDistance
                    );
                    wickGradient.addColorStop(0, upperWickColor);
                    wickGradient.addColorStop(0.7, upperWickColor);
                    wickGradient.addColorStop(1, upperWickColor + '00'); // Fade to transparent

                    context.setAttr('strokeStyle', wickGradient);
                    context.setAttr('lineWidth', wickWidth);
                    context.beginPath();
                    context.moveTo(x, highY);
                    context.lineTo(x, Math.min(closeY, openY) + meltDistance);
                    context.stroke();
                }

                // Lower wick with melting gradient
                if (lowY > Math.max(closeY, openY)) {
                    const wickGradient = context.createLinearGradient(
                        x, Math.max(closeY, openY) - meltDistance,
                        x, lowY
                    );
                    wickGradient.addColorStop(0, lowerWickColor + '00'); // Start transparent
                    wickGradient.addColorStop(0.3, lowerWickColor);
                    wickGradient.addColorStop(1, lowerWickColor);

                    context.setAttr('strokeStyle', wickGradient);
                    context.setAttr('lineWidth', wickWidth);
                    context.beginPath();
                    context.moveTo(x, Math.max(closeY, openY) - meltDistance);
                    context.lineTo(x, lowY);
                    context.stroke();
                }

                // Draw body - use rounded corners only if body is large enough
                context.beginPath();
                if (bodyHeight < minHeightForRoundCorners) {
                    // For small bodies, use simple rectangle
                    context.rect(bodyX, bodyY, barWidth, bodyHeight);
                } else {
                    // For larger bodies, use rounded corners
                    context.moveTo(bodyX + cornerRadius, bodyY);
                    context.lineTo(bodyX + barWidth - cornerRadius, bodyY);
                    context.arcTo(bodyX + barWidth, bodyY, bodyX + barWidth, bodyY + cornerRadius, cornerRadius);
                    context.lineTo(bodyX + barWidth, bodyY + bodyHeight - cornerRadius);
                    context.arcTo(bodyX + barWidth, bodyY + bodyHeight, bodyX + barWidth - cornerRadius, bodyY + bodyHeight, cornerRadius);
                    context.lineTo(bodyX + cornerRadius, bodyY + bodyHeight);
                    context.arcTo(bodyX, bodyY + bodyHeight, bodyX, bodyY + bodyHeight - cornerRadius, cornerRadius);
                    context.lineTo(bodyX, bodyY + cornerRadius);
                    context.arcTo(bodyX, bodyY, bodyX + cornerRadius, bodyY, cornerRadius);
                }
                context.closePath();

                context.setAttr('fillStyle', gradient);
                context.fill();
            }
        }
    }


    return (
        <Shape
            sceneFunc={sceneFunc}
        />
    )
}

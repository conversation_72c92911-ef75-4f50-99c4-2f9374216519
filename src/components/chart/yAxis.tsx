import { Layer, Stage } from 'react-konva'
import { useChartContext } from '../../contexts/chartContext';
import Konva from 'konva';
import { useAppContext } from '../../contexts/appContext';
import { usePaneScale } from '../../hooks/useScale';
import { YAxisContainer } from '../../styles/chartStyles';

export const YAxis = () => {
    console.debug('rendering y axis')
    const { valueRange, autoScale, yAxisWidth, height } = useChartContext()
    const { resetValueRange } = usePaneScale()
    const { activeChart, crosshairsCoord } = useAppContext()

    return (
        <YAxisContainer>
            <Stage
                width={yAxisWidth}
                height={height}
                onWheel={onWheel}
                onDblClick={onDblClick}
                onMouseEnter={onMouseEnter}
            >
                <Layer>
                </Layer>
            </Stage>
        </YAxisContainer>

    )

    function onMouseEnter(e: Konva.KonvaEventObject<MouseEvent>) {
        crosshairsCoord.value = null
        activeChart.value = ''
    }

    function onWheel(e: Konva.KonvaEventObject<WheelEvent>) {
        e.evt.preventDefault()
        const wheelDelta = e.evt.deltaY
        const zoomFactor = Math.pow(0.999, -wheelDelta);

        const prev = valueRange.value
        if (!prev) return
        const span = prev.max - prev.min
        const newSpan = span * zoomFactor
        const centerTime = prev.min + (span / 2)
        const newMin = centerTime - (newSpan / 2)
        const newMax = centerTime + (newSpan / 2)
        valueRange.value = {
            min: newMin,
            max: newMax
        }
        autoScale.value = false
    }

    function onDblClick(_e: Konva.KonvaEventObject<MouseEvent>) {
        autoScale.value = true
        resetValueRange()
    }
}

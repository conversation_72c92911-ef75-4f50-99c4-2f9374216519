<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Twisted Glass Distortion with Blur</title>
    <!-- Tailwind CSS for basic styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Apply the Inter font for consistent typography */
        body {
            font-family: "Inter", sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f0f0f0;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
            overflow: hidden;
            /* Prevent scrollbars if distortion expands elements */
        }

        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        /* Styles for the filtered element */
        .filtered-element {
            /* Removed font styles as content is now an image */
            padding: 20px;
            border-radius: 8px;
            /* Light green background */
            filter: url(#glass-distortion-with-blur);
            /* Apply the SVG filter here via CSS */
            /* Smooth transition for hover */
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            /* Important for filters that might expand content */
            width: 100%;
            /* Ensure it takes full width within container */
            max-width: 400px;
            /* Limit image size for better viewing */
            height: 300px;
            /* Fixed height for consistency */
        }

        .filtered-element img {
            max-width: 100%;
            height: 100%;
            object-fit: cover;
            /* Ensures the image covers the area, might get distorted */
            border-radius: 6px;
            /* Match container rounding */
        }

        /* The SVG itself can be hidden as it only contains filter definitions */
        svg {
            position: absolute;
            width: 0;
            height: 0;
            overflow: hidden;
            pointer-events: none;
            /* Ensure it doesn't interfere with mouse events */
        }
    </style>
</head>

<body>

    <div class="container"
        style="background-image: url('https://raw.githubusercontent.com/lucasromerodb/liquid-glass-effect-macos/refs/heads/main/assets/flowers.jpg');">
        <h1 class="text-3xl font-bold mb-6 text-gray-800">Twisted Glass Distortion with Blur</h1>
        <p class="text-gray-600 mb-8">
            This example applies an enhanced SVG filter with blur and a more intense, stretched distortion to the image
            below.
            Move your mouse over the image to see a subtle hover effect.
        </p>

        <!-- The element to which the filter will be applied -->
        <div class="filtered-element">
            <img src='https://raw.githubusercontent.com/lucasromerodb/liquid-glass-effect-macos/refs/heads/main/assets/flowers.jpg'
                alt="Beautiful flowers"
                onerror="this.onerror=null; this.src='https://placehold.co/400x300/cccccc/000000?text=Image+Load+Error';">
        </div>
    </div>

    <!-- SVG definition for the filter -->
    <svg>
        <!-- Main Filter for default state -->
        <filter id="glass-distortion-with-blur" x="0%" y="0%" width="100%" height="100%" filterUnits="objectBoundingBox"
            color-interpolation-filters="sRGB">
            <!-- STEP 1: Apply a blur to the SourceGraphic (the element itself) -->
            <feGaussianBlur in="SourceGraphic" stdDeviation="8" result="initialBlur" />

            <!-- STEP 2: Generate Perlin noise for distortion.
                 Increased baseFrequency, especially in Y, and more octaves create a more
                 stretched and complex, 'bending light' like noise pattern. -->
            <feTurbulence type="fractalNoise" baseFrequency="0.01 0.01" numOctaves="1" seed="5"
                result="turbulenceNoise" />

            <!-- STEP 3: Blurs the noise to create smoother transitions for displacement -->
            <feGaussianBlur in="turbulenceNoise" stdDeviation="5" result="blurredNoise" />

            <!-- STEP 4: Distorts the initially blurred graphic based on the R and G channels of blurredNoise.
                 Significantly increased 'scale' for a more intense, twisted, and stretched effect. -->
            <feDisplacementMap in="initialBlur" in2="blurredNoise" scale="150" xChannelSelector="R"
                yChannelSelector="G" />
        </filter>
    </svg>

</body>

</html>